
from backend.actions.relationship_summarize import RelationshipSummarize

async def test():
    char_zh = "星瞳"
    print("测试关系总结")
    print("测试模型：hunyuan-large-longcontext")
    rs = RelationshipSummarize(char_zh, "hunyuan-large-longcontext")
    res = await rs.run(100)
    print("测试模型：qwen2.5-72b")
    rs = RelationshipSummarize(char_zh, "qwen2.5-72b")
    res = await rs.run(100)

    print("测试模型：qwen3-235b-a22b:free") #ok
    rs = RelationshipSummarize(char_zh, "qwen3-235b-a22b:free")
    res = await rs.run(100)

    print("测试模型：claude") #ok
    rs = RelationshipSummarize(char_zh)
    res = await rs.run(100)

    print("测试模型：qwen3-32b:free")
    rs = RelationshipSummarize(char_zh, "qwen3-32b:free")
    res = await rs.run(100)

    print("测试模型：deepseek-prove-v2:free")
    rs = RelationshipSummarize(char_zh, "deepseek-prove-v2:free")
    res = await rs.run(100)

    print("测试模型：deepseek-v3-0324:free") # ok
    rs = RelationshipSummarize(char_zh, "deepseek-v3-0324:free")
    res = await rs.run(100)

    print("测试模型：deepseek-r1:free") # ok
    rs = RelationshipSummarize(char_zh, "deepseek-r1:free")
    res = await rs.run(100)

if __name__ == "__main__":
    import asyncio
    asyncio.run(test())