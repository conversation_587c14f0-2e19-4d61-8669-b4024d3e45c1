
from backend.actions.reasoning_of_fans import ReasoningOfFans
from backend.tools.query_user_data import query_recent_info

async def test():
    char_zh = "星瞳"
    mid = "401315430"
    print("测试关系总结")
    recent_info = await query_recent_info(mid)

    print("测试模型：hunyuan-large-longcontext")
    rs = ReasoningOfFans(char_zh, "hunyuan-large-longcontext")
    res = await rs.run(recent_info)
    print("测试模型：qwen2.5-72b")
    rs = ReasoningOfFans(char_zh, "qwen2.5-72b")
    res = await rs.run(recent_info)

    print("测试模型：qwen3-235b-a22b:free") #ok
    rs = ReasoningOfFans(char_zh, "qwen3-235b-a22b:free")
    res = await rs.run(recent_info)

    print("测试模型：claude") #ok
    rs = ReasoningOfFans(char_zh)
    res = await rs.run(recent_info)

    print("测试模型：qwen3-32b:free")
    rs = ReasoningOfFans(char_zh, "qwen3-32b:free")
    res = await rs.run(recent_info)

    print("测试模型：deepseek-prove-v2:free") 
    rs = ReasoningOfFans(char_zh, "deepseek-prove-v2:free")
    res = await rs.run(recent_info)

    print("测试模型：deepseek-v3-0324:free") # ok
    rs = ReasoningOfFans(char_zh, "deepseek-v3-0324:free")
    res = await rs.run(recent_info)

    print("测试模型：deepseek-r1:free") # ok
    rs = ReasoningOfFans(char_zh, "deepseek-r1:free")
    res = await rs.run(recent_info)

if __name__ == "__main__":
    import asyncio
    asyncio.run(test())