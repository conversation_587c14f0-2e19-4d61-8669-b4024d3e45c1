import base64
import json
import math
import os
import re
import struct
import urllib.parse
from datetime import datetime, timedelta

import bs4
import httpx
import requests
from langchain_community.document_loaders import (
    DirectoryLoader,
    TextLoader,
    WebBaseLoader,
)

from const import COOKIE
from logger import logger


def safe_int(value):
    try:
        return int(value)
    except ValueError:
        return 0


def base64_to_float_array(base64_data):
    byte_array = base64.b64decode(base64_data)

    float_array = []

    for i in range(0, len(byte_array), 4):
        num = struct.unpack("!f", byte_array[i : i + 4])[0]
        float_array.append(num)

    return float_array


def video_calculate_hotness(views, comments, honors, likes, coins, favorites):
    max_views = 5000000
    max_comments = 20000
    max_honors = 4
    max_likes = 100000
    max_coins = 50000
    max_favors = 20000

    hotness = (
        round(2.4 * math.log10(views + 1) / math.log10(max_views), 2)
        + round(0.8 * math.log10(comments + 1) / math.log10(max_comments), 2)
        + round(1.6 * math.log10(likes + 1) / math.log10(max_likes), 2)
        + round(1.6 * math.log10(coins + 1) / math.log10(max_coins), 2)
        + round(1.6 * math.log10(favorites + 1) / math.log10(max_favors), 2)
        + honors * 2 / max_honors
    )
    return min(10, hotness)


def dynamic_calculate_hotness(sends, comments, likes):
    max_sends = 5000
    max_comments = 25000
    max_likes = 40000

    hotness = (
        3 * math.log10(min(sends + 1, max_sends)) / math.log10(max_sends)
        + 4 * math.log10(min(comments + 1, max_comments)) / math.log10(max_comments)
        + 3 * math.log10(min(likes + 1, max_likes)) / math.log10(max_likes)
    )
    return min(10, hotness)


# compute cosine similarity between two vector
# def get_cosine_similarity(v1, v2, device):
#     import torch
#     v1 = torch.tensor(v1).to(device)
#     v2 = torch.tensor(v2).to(device)
#     return torch.cosine_similarity(v1, v2, dim=0).item()


def save_txt(filename, content, output_path):
    """
    Give in Whole path name, save to output_path whole_path
    """
    output_path = os.path.join(
        output_path, f"{os.path.splitext(os.path.basename(filename))[0]}.txt"
    )
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(content)


def get_access_token():
    API_KEY = os.getenv("StoryAudit_API_AK")
    SECRET_KEY = os.getenv("StoryAudit_API_SK")

    """
    :return: access_token, or None
    """
    url = "https://aip.baidubce.com/oauth/2.0/token"
    params = {
        "grant_type": "client_credentials",
        "client_id": API_KEY,
        "client_secret": SECRET_KEY,
    }
    return str(requests.post(url, params=params).json().get("access_token"))


def text_censor(text):
    """
    :param text: text to be censored
    """
    request_url = (
        "https://aip.baidubce.com/rest/2.0/solution/v1/text_censor/v2/user_defined"
    )

    params = {"text": text}
    access_token = get_access_token()
    request_url = request_url + "?access_token=" + access_token
    headers = {"content-type": "application/x-www-form-urlencoded"}
    response = requests.post(request_url, data=params, headers=headers)
    return response.json()["conclusion"] == "合规"


def extract_info_from_file_name(filename):
    """ "
    Excact title/time/bv from filename
    """
    base_name = os.path.splitext(filename)[0]

    pattern = r"【.*?】(.*?)_(BV\w+)"
    match = re.search(pattern, base_name)

    if match:
        title_with_time = match.group(1).strip()
        bv = match.group(2).strip()

        time_pattern = r"(\d{4}年\d{1,2}月\d{1,2}日\d{1,2}点场)"
        time_match = re.search(time_pattern, base_name)

        if time_match:
            time = time_match.group(1).strip()
            title = title_with_time.replace(time, "").strip()
        else:
            time = "无"
            title = title_with_time

        return title, time, bv
    else:
        return None, None, None


def dir_loader(dir_path):
    logger.info("Loading directory: {}".format(dir_path))
    loader = DirectoryLoader(
        dir_path,
        glob="**/*.txt",
        loader_cls=TextLoader,
        loader_kwargs={"encoding": "utf-8"},
        show_progress=True,
        use_multithreading=True,
        silent_errors=True,
        max_concurrency=20,
    )
    docs = loader.load()
    return docs


def web_loader(web_url):
    os.environ["User-Agent"] = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4146.4 Safari/537.36"
    )
    loader = WebBaseLoader(
        web_paths=(web_url,),
        bs_kwargs=dict(
            parse_only=bs4.SoupStrainer(
                class_=("post-content", "post-title", "post-header", "mw-parser-output")
            )
        ),
        encoding="utf-8",
    )
    docs = loader.load()
    return docs


def check_url(url):
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            logger.info(f"URL {url} is accessible.")
            return True
        else:
            logger.info(f"URL {url} returned status code {response.status_code}.")
            return False
    except requests.RequestException as e:
        logger.info(f"Error accessing URL {url}: {e}")
        return False


def remove_pattern(text, pattern=r'\[\b\d+\b\]'):
    result = re.sub(pattern, "", text)
    return result


def read_vups_config():
    """
    Read VUP configuration from vups.json file

    Returns:
        list: List of VUP configuration dictionaries
    """
    import json
    from const import PROJECT_ROOT

    config_path = f"{PROJECT_ROOT}/config/vups.json"
    with open(config_path, "r", encoding="utf-8") as file:
        return json.load(file)


def get_vup_by_short_name(short_name):
    """
    Get VUP configuration by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        dict: VUP configuration dictionary or None if not found
    """
    vups = read_vups_config()
    for vup in vups:
        if vup["shortName"] == short_name:
            return vup
    return None


def get_vup_uid_by_short_name(short_name):
    """
    Get VUP UID by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        int: VUP UID or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["uid"] if vup else None


def get_vup_uid_by_any_name(name):
    """
    Get VUP UID by any name (fullName, shortName, or enName)

    Args:
        name (str): Any name of the VUP (fullName, shortName, or enName)

    Returns:
        int: VUP UID or None if not found
    """
    vups = read_vups_config()

    # Check shortName first
    for vup in vups:
        if vup["shortName"] == name:
            return vup["uid"]

    # Then check enName
    for vup in vups:
        if vup["enName"] == name:
            return vup["uid"]

    # Finally check fullName
    for vup in vups:
        if vup["fullName"] == name:
            return vup["uid"]

    return None


def get_vup_room_id_by_short_name(short_name):
    """
    Get VUP room ID by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        int: VUP room ID or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["roomId"] if vup else None


def get_vup_tieba_name_by_short_name(short_name):
    """
    Get VUP tieba name by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        str: VUP tieba name or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["tiebaName"] if vup else None


def get_vup_en_name_by_short_name(short_name):
    """
    Get VUP English name by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        str: VUP English name or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["enName"] if vup else None


def get_vtuber_list():
    """
    Get list of all VTuber short names from vups.json

    Returns:
        list: List of VTuber short names
    """
    vups = read_vups_config()
    return [vup["shortName"] for vup in vups]


def remove_duplicates_preserve_order(lst):
    seen = set()
    result = []
    for item in lst:
        if item not in seen:
            seen.add(item)
            result.append(item)
    return result


def get_random_emoji():
    import random

    emojis = [
        "😎",
        "🙌",
        "🤔",
        "😶‍🌫️",
        "😆",
        "😅",
        "😂",
        "🤣",
        "😉",
        "😊",
        "😇",
        "🙂",
        "🙃",
        "😉",
        "😌",
        "🐱",
        "🐶",
        "😆",
        "🐹",
        "🐰",
        "🙊",
        "🤷‍♀️",
        "🥲",
        "🤨",
        "😏",
        "😒",
        "🤪",
        "🥺",
        "😟",
        "😕",
        "🤭",
        "🤡",
    ]
    return random.choice(emojis)


def chatbot_response_parser(response: str):
    # remove ~ cancle line ~
    context = response.replace("~", "\\~")
    return context


XOR_CODE = 23442827791579
MASK_CODE = 2251799813685247
MAX_AID = 1 << 51
ALPHABET = "FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf"
ENCODE_MAP = 8, 7, 0, 5, 1, 3, 2, 4, 6
DECODE_MAP = tuple(reversed(ENCODE_MAP))

BASE = len(ALPHABET)
PREFIX = "BV1"
PREFIX_LEN = len(PREFIX)
CODE_LEN = len(ENCODE_MAP)


def av2bv(aid: int) -> str:
    bvid = [""] * 9
    tmp = (MAX_AID | aid) ^ XOR_CODE
    for i in range(CODE_LEN):
        bvid[ENCODE_MAP[i]] = ALPHABET[tmp % BASE]
        tmp //= BASE
    return PREFIX + "".join(bvid)


def bv2av(bvid: str):
    assert bvid[:3] == PREFIX

    bvid = bvid[3:]
    tmp = 0
    for i in range(CODE_LEN):
        idx = ALPHABET.index(bvid[DECODE_MAP[i]])
        tmp = tmp * BASE + idx
    return (tmp & MASK_CODE) ^ XOR_CODE


def parse_path(title) -> str:
    pattern = r'[\\/:"*?<>|]+'
    parsed_title = re.sub(pattern, "", title)
    return parsed_title


def parse_chars(string) -> str:
    # all unicode characters from 0x0000 - 0x0020 (33 total) are bad and will be replaced by "" (empty string)
    temp_list = list(string)
    for pos in range(len(temp_list)):
        if ord(temp_list[pos]) < 32:
            temp_list[pos] = ""
        return "".join(temp_list)


def convert_ms_to_hms(milliseconds):
    seconds = int((milliseconds / 1000) % 60)
    minutes = int((milliseconds / (1000 * 60)) % 60)
    hours = int((milliseconds / (1000 * 60 * 60)))

    return f"{hours}:{minutes}:{seconds}"


def midhash_to_mid(midhash) -> str:
    # TODO
    pass


def get_cookie():
    import time

    import requests
    from PIL import Image
    from qrcode.main import QRCode

    url = "https://passport.bilibili.com/x/passport-login/web/qrcode/generate?source=main-fe-header"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.0",
        "Referer": "https://www.bilibili.com/",
        "Origin": "https://www.bilibili.com",
    }

    response = requests.get(url=url, headers=headers).json()
    print(response)
    qrcode_key = response["data"]["qrcode_key"]
    print(qrcode_key)
    qr = QRCode()
    qr.add_data(response["data"]["url"])
    img = qr.make_image()
    img = img.resize((200, 200), resample=Image.BICUBIC)
    img.show()

    check_login_url = f"https://passport.bilibili.com/x/passport-login/web/qrcode/poll?qrcode_key={qrcode_key}&source=main-fe-header"
    session = requests.Session()
    while 1:
        data = session.get(url=check_login_url, headers=headers).json()
        print(data)
        if data["data"]["code"] == 0:
            response = session.get("https://www.bilibili.com/", headers=headers)
            print(session.cookies.get_dict())
            with open("cookies.txt", "w") as f:
                f.write(str(session.cookies.get_dict()))
            break

        time.sleep(1)


def get_date_range(recent_days: int):
    end_date = datetime.now()
    start_date = end_date - timedelta(days=recent_days)

    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")

    return start_date_str, end_date_str


async def get_user_dynamic_render_data(uid):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
        "Referer": "https://www.bilibili.com/",
        "Cookie": COOKIE,
    }

    dynamic_url = f"https://space.bilibili.com/{uid}/dynamic"

    text = httpx.get(dynamic_url, headers=headers).text

    # <script id="__RENDER_DATA__" type="application/json">xxx</script>
    __RENDER_DATA__ = re.search(
        r"<script id=\"__RENDER_DATA__\" type=\"application/json\">(.*?)</script>",
        text,
        re.S,
    ).group(1)
    render_data = json.loads(urllib.parse.unquote(__RENDER_DATA__))
    return render_data


# get_cookie()


def get_csv_files(directory_path):
    csv_files = []
    if os.path.exists(directory_path):
        for filename in os.listdir(directory_path):
            if filename.endswith(".csv"):
                file_path = os.path.join(directory_path, filename)
                csv_files.append(file_path)
    return csv_files


def time_parse(time_str):
    def parse_relative_time(time_str):
        now = datetime.now()
        if "分钟前" in time_str:
            minutes = int(re.search(r"(\d+)分钟前", time_str).group(1))
            return now - timedelta(minutes=minutes)
        elif "小时前" in time_str:
            hours = int(re.search(r"(\d+)小时前", time_str).group(1))
            return now - timedelta(hours=hours)
        elif "天前" in time_str:
            days = int(re.search(r"(\d+)天前", time_str).group(1))
            return now - timedelta(days=days)
        elif "昨天" in time_str:
            time_part = re.search(r"昨天 (\d{2}:\d{2})", time_str).group(1)
            yesterday = now - timedelta(days=1)
            return datetime.strptime(
                f"{yesterday.date()} {time_part}", "%Y-%m-%d %H:%M"
            )
        else:
            return None

    def parse_absolute_time(time_str):
        try:
            return datetime.strptime(time_str, "%Y年%m月%d日")
        except ValueError:
            try:
                date_without_year = datetime.strptime(time_str, "%m月%d日")
                current_year = datetime.now().year
                return date_without_year.replace(year=current_year)
            except ValueError:
                return None

    relative_time = parse_relative_time(time_str)
    if relative_time:
        return relative_time
    absolute_time = parse_absolute_time(time_str)
    if absolute_time:
        return absolute_time
    return None


def convert_unix_ms_to_ymd(unix_time_ms):
    unix_time = unix_time_ms / 1000.0
    dt_object = datetime.fromtimestamp(unix_time)
    formatted_date = dt_object.strftime("%Y-%m-%d")
    return formatted_date


def convert_unix_ms_to_ymdhms(unix_time_ms):
    unix_time = unix_time_ms / 1000.0
    dt_object = datetime.fromtimestamp(unix_time)
    formatted_date = dt_object.strftime("%Y-%m-%d %H:%M:%S")
    return formatted_date


def convert_unix_ms_to_ymdh(unix_time_ms):
    unix_time = unix_time_ms / 1000.0
    dt_object = datetime.fromtimestamp(unix_time)
    formatted_date = dt_object.strftime("%Y%m%d%H")
    return formatted_date
