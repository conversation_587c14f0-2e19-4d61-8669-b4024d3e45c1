#!/usr/bin/env python3
"""
简化版视频统计数据分析脚本
Simplified Video Statistics Analysis Script

直接使用SQL查询分析问题，不依赖复杂的模块
"""

import asyncio
import asyncpg
import os
from pathlib import Path

# 数据库连接配置 - 请根据实际情况修改
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'vupbi',  # 请修改为实际数据库名
    'user': 'postgres',   # 请修改为实际用户名
    'password': 'password'  # 请修改为实际密码
}

async def get_db_connection():
    """获取数据库连接"""
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        print("请检查数据库配置信息")
        return None

async def analyze_video_stats():
    """分析视频统计数据"""
    
    conn = await get_db_connection()
    if not conn:
        return
    
    try:
        print("=" * 80)
        print("视频统计数据分析报告")
        print("=" * 80)
        
        # 查询总体统计
        total_query = """
        SELECT 
            COUNT(*) as total_videos,
            COUNT(DISTINCT uid) as total_users,
            SUM(CASE WHEN like_num IS NULL THEN 1 ELSE 0 END) as null_like_num,
            SUM(CASE WHEN coin IS NULL THEN 1 ELSE 0 END) as null_coin,
            SUM(CASE WHEN favorite_num IS NULL THEN 1 ELSE 0 END) as null_favorite_num,
            SUM(CASE WHEN share_num IS NULL THEN 1 ELSE 0 END) as null_share_num,
            SUM(CASE WHEN danmuku_num IS NULL THEN 1 ELSE 0 END) as null_danmuku_num,
            SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL THEN 1 ELSE 0 END) as need_fix
        FROM videos_table
        """
        
        result = await conn.fetchrow(total_query)
        
        total_videos = result['total_videos']
        total_users = result['total_users']
        null_like = result['null_like_num']
        null_coin = result['null_coin']
        null_favorite = result['null_favorite_num']
        null_share = result['null_share_num']
        null_danmuku = result['null_danmuku_num']
        need_fix = result['need_fix']
        
        print(f"总体统计:")
        print(f"  总视频数: {total_videos:,}")
        print(f"  总用户数: {total_users:,}")
        print(f"  需要修复的视频: {need_fix:,} ({need_fix/total_videos*100:.1f}%)")
        print()
        
        print(f"各字段null值统计:")
        print(f"  like_num (点赞数):    {null_like:,} ({null_like/total_videos*100:.1f}%)")
        print(f"  coin (投币数):        {null_coin:,} ({null_coin/total_videos*100:.1f}%)")
        print(f"  favorite_num (收藏数): {null_favorite:,} ({null_favorite/total_videos*100:.1f}%)")
        print(f"  share_num (分享数):   {null_share:,} ({null_share/total_videos*100:.1f}%)")
        print(f"  danmuku_num (弹幕数): {null_danmuku:,} ({null_danmuku/total_videos*100:.1f}%)")
        print()
        
        # 按用户分析
        user_query = """
        SELECT 
            uid,
            name,
            COUNT(*) as total_videos,
            SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL THEN 1 ELSE 0 END) as need_fix
        FROM videos_table
        GROUP BY uid, name
        HAVING SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL THEN 1 ELSE 0 END) > 0
        ORDER BY need_fix DESC
        """
        
        user_results = await conn.fetch(user_query)
        
        if user_results:
            print(f"按用户统计 (有问题的用户):")
            print(f"{'用户ID':<15} {'用户名':<15} {'总视频数':<10} {'需修复':<10} {'比例':<10}")
            print("-" * 70)
            
            for row in user_results:
                uid = row['uid']
                name = (row['name'] or 'Unknown')[:13]
                total = row['total_videos']
                need_fix_user = row['need_fix']
                ratio = need_fix_user / total * 100
                print(f"{uid:<15} {name:<15} {total:<10} {need_fix_user:<10} {ratio:.1f}%")
            
            print()
        
        # 最近的视频分析
        recent_query = """
        SELECT 
            bvid, video_name, uid, datetime,
            like_num, coin, favorite_num, share_num, danmuku_num
        FROM videos_table 
        WHERE like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
        ORDER BY datetime DESC 
        LIMIT 10
        """
        
        recent_results = await conn.fetch(recent_query)
        
        if recent_results:
            print(f"最近的问题视频 (前10个):")
            print(f"{'BVID':<15} {'发布时间':<12} {'用户ID':<12} {'视频标题':<30}")
            print("-" * 80)
            
            for row in recent_results:
                bvid = row['bvid']
                video_name = (row['video_name'] or 'Unknown')[:28]
                uid = row['uid']
                datetime_str = row['datetime'].strftime('%Y-%m-%d') if row['datetime'] else 'Unknown'
                print(f"{bvid:<15} {datetime_str:<12} {uid:<12} {video_name:<30}")
            
            print()
        
        # 统计字段组合分析
        combo_query = """
        SELECT 
            CASE WHEN like_num IS NULL THEN 'NULL' ELSE 'OK' END as like_status,
            CASE WHEN coin IS NULL THEN 'NULL' ELSE 'OK' END as coin_status,
            CASE WHEN favorite_num IS NULL THEN 'NULL' ELSE 'OK' END as favorite_status,
            COUNT(*) as count
        FROM videos_table
        GROUP BY 
            CASE WHEN like_num IS NULL THEN 'NULL' ELSE 'OK' END,
            CASE WHEN coin IS NULL THEN 'NULL' ELSE 'OK' END,
            CASE WHEN favorite_num IS NULL THEN 'NULL' ELSE 'OK' END
        ORDER BY count DESC
        """
        
        combo_results = await conn.fetch(combo_query)
        
        if combo_results:
            print(f"字段组合分析:")
            print(f"{'点赞':<8} {'投币':<8} {'收藏':<8} {'数量':<10} {'比例':<10}")
            print("-" * 50)
            
            for row in combo_results:
                like_status = row['like_status']
                coin_status = row['coin_status']
                favorite_status = row['favorite_status']
                count = row['count']
                ratio = count / total_videos * 100
                print(f"{like_status:<8} {coin_status:<8} {favorite_status:<8} {count:<10} {ratio:.1f}%")
        
        print("=" * 80)
        print("分析完成")
        print()
        print("建议操作:")
        if need_fix > 0:
            print(f"1. 运行修复脚本: python fix/fix_video_stats.py --dry-run")
            print(f"2. 确认无误后执行: python fix/fix_video_stats.py")
            if len(user_results) == 1:
                uid = user_results[0]['uid']
                print(f"3. 或针对单个用户: python fix/fix_video_stats.py --uid {uid}")
            print(f"4. 快速修复: python fix/quick_fix_existing_videos.py")
        else:
            print("✓ 所有视频统计数据完整，无需修复")
        print("=" * 80)
        
    except Exception as e:
        print(f"分析过程中发生错误: {e}")
    finally:
        await conn.close()

def print_usage():
    """打印使用说明"""
    print("使用前请先配置数据库连接信息:")
    print("编辑 fix/simple_analyze.py 文件中的 DB_CONFIG 变量")
    print()
    print("示例配置:")
    print("DB_CONFIG = {")
    print("    'host': 'localhost',")
    print("    'port': 5432,")
    print("    'database': 'your_database_name',")
    print("    'user': 'your_username',")
    print("    'password': 'your_password'")
    print("}")

async def main():
    """主函数"""
    try:
        await analyze_video_stats()
    except Exception as e:
        print(f"程序执行失败: {e}")
        print()
        print_usage()

if __name__ == "__main__":
    asyncio.run(main())
