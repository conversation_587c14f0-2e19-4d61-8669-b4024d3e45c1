# 视频统计数据修复脚本

## 问题描述

在 `server/user_info_till_server.py` 的 `fetch_all_video` 函数中，存在以下问题导致 `like_num`、`coin`、`favorite_num` 等字段出现大量 null 值：

1. **只处理新视频**：函数只对新发现的视频（不在数据库中的BVID）调用 `get_all_video_info()` 获取详细统计信息
2. **API调用失败**：当 `get_all_video_info()` 调用失败时，异常处理会将统计字段设置为 None
3. **频率控制不足**：API调用间隔过短可能触发平台风控机制

## 解决方案

本修复脚本 `fix_video_stats.py` 提供以下功能：

- 查询数据库中所有统计字段为 null 的视频记录
- 重新调用 bilibili API 获取完整的视频统计数据
- 安全地更新数据库，不会影响其他字段
- 包含频率控制机制，避免触发风控
- 提供详细的日志记录和错误处理

## 使用方法

### 基本用法

```bash
# 试运行模式，查看需要修复的视频数量
python fix/fix_video_stats.py --dry-run

# 修复所有用户的视频统计数据
python fix/fix_video_stats.py

# 只修复特定用户的视频
python fix/fix_video_stats.py --uid "401315430"

# 自定义批处理大小和延时
python fix/fix_video_stats.py --batch-size 5 --delay 8.0

# 重试之前失败的视频
python fix/fix_video_stats.py --retry-failed
```

### 参数说明

- `--uid UID`: 指定用户ID，只处理该用户的视频
- `--batch-size N`: 批处理大小，默认10个视频为一批
- `--delay N`: API调用延时（秒），默认5秒，建议不少于3秒
- `--dry-run`: 试运行模式，只查询不更新数据库
- `--retry-failed`: 重试之前失败的记录
- `--help`: 显示帮助信息

### 安全特性

1. **数据安全**：
   - 使用 UPDATE 语句，只更新统计字段
   - 不会删除或覆盖现有数据
   - 支持试运行模式预览操作

2. **频率控制**：
   - 每次API调用间隔5秒（可调整）
   - 添加随机延时模拟人工操作
   - 批量处理间隔10-20秒

3. **错误处理**：
   - 记录失败的视频BVID到文件
   - 支持重试失败的记录
   - 详细的日志记录

## 执行流程

1. **查询阶段**：扫描数据库找出统计字段为 null 的视频
2. **确认阶段**：显示待处理视频数量，等待用户确认
3. **处理阶段**：逐个调用API获取统计数据并更新数据库
4. **结果阶段**：显示处理结果，保存失败记录

## 日志文件

脚本会生成以下文件：

- `fix/fix_video_stats_YYYYMMDD_HHMMSS.log`: 详细执行日志
- `fix/failed_videos.json`: 失败的视频BVID列表（用于重试）

## 注意事项

1. **网络环境**：确保网络连接稳定，避免API调用失败
2. **执行时间**：大量视频的修复可能需要较长时间
3. **风控机制**：如果遇到频繁的API失败，建议增加延时参数
4. **数据库连接**：确保数据库连接配置正确

## 示例输出

```
2025-08-11 20:30:00 - INFO - 找到 150 个需要修复统计信息的视频

即将修复 150 个视频的统计信息
批处理大小: 10
API调用延时: 5.0 秒

确认继续？(y/N): y

2025-08-11 20:30:05 - INFO - 处理进度: 1/150 - 视频: BV1234567890 (测试视频)
2025-08-11 20:30:10 - INFO - 正在获取视频 BV1234567890 的统计信息...
2025-08-11 20:30:15 - INFO - 视频 BV1234567890 统计信息: {'like_num': 1234, 'coin': 567, ...}
2025-08-11 20:30:16 - INFO - 成功更新视频 BV1234567890 的统计信息
...

修复完成!
总处理时间: 1800.5 秒
成功修复: 145 个视频
失败: 5 个视频
失败的视频已保存到: fix/failed_videos.json
```

## 故障排除

### 常见问题

1. **API调用失败**：
   - 检查网络连接
   - 增加延时参数 `--delay 10`
   - 检查bilibili认证信息

2. **数据库连接失败**：
   - 检查数据库配置
   - 确认数据库服务运行正常

3. **权限问题**：
   - 确保有数据库写入权限
   - 检查文件系统写入权限

### 重试失败的视频

如果有视频处理失败，可以使用以下命令重试：

```bash
python fix/fix_video_stats.py --retry-failed --delay 10
```
